<?php

namespace local_degreed_integration\degreed\entities\content;

class book extends abstract_content {

    public ?string $subtitle       = null;
    public ?string $authors        = null;
    public ?int    $pages          = null;
    public ?string $image_url      = null;
    public ?bool   $obsolete       = null;
    public ?string $publish_date   = null;
    public ?string $language       = null;
    public ?string $isbn13         = null;
    public ?string $format         = null;
    public ?string $owner_id       = null;
    public ?string $owner_type     = null;

    public function get_type() : string {
        return 'Book';
    }

    public function get_api_resource_name() : string {
        return 'books';
    }

    protected function define_to_api_attributes() : array {
        return [
            'external-id'   => $this->external_id,
            'title'         => $this->title,
            'subtitle'      => $this->subtitle,
            'authors'       => $this->authors,
            'pages'         => $this->pages,
            'summary'       => $this->summary,
            'image-url'     => $this->image_url,
            'obsolete'      => $this->obsolete,
            'publish-date'  => $this->publish_date,
            'language'      => $this->language,
            'i-s-b-n-13'    => $this->isbn13,
            'format'        => $this->format,
            'owner-id'      => $this->owner_id,
            'owner-type'    => $this->owner_type,
        ];
    }

    protected static function from_api_data(array $data) : static {
        $attributes = $data['attributes'];

        $instance = new static([
            'id'            => $data['id'],
            'external_id'   => $attributes['external-id'],
            'title'         => $attributes['title']        ?? null,
            'summary'       => $attributes['summary']      ?? null,
            'subtitle'      => $attributes['subtitle']     ?? null,
            'authors'       => $attributes['authors']      ?? null,
            'pages'         => $attributes['pages']        ?? null,
            'image_url'     => $attributes['image-url']    ?? null,
            'obsolete'      => $attributes['obsolete']     ?? null,
            'publish_date'  => $attributes['publish-date'] ?? null,
            'language'      => $attributes['language']     ?? null,
            'isbn13'        => $attributes['i-s-b-n-13']   ?? null,
            'format'        => $attributes['format']       ?? null,
            'owner_id'      => $attributes['owner-id']     ?? null,
            'owner_type'    => $attributes['owner-type']   ?? null,
        ]);

        return $instance;
    }
}
