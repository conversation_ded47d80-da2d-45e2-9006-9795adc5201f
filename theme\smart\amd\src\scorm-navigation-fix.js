/**
 * SCORM Navigation Auto-Update Fix
 * 
 * <PERSON><PERSON> módulo resolve o problema onde os botões de navegação SCORM
 * não são habilitados automaticamente após a conclusão de um módulo.
 * 
 * Funcionalidades:
 * - Monitora chamadas LMSFinish/LMSCommit para detectar conclusão
 * - Força atualização da navegação após conclusão
 * - Polling periódico como backup
 * - Compatível com navegação header (nav_display === 3)
 * 
 * @module theme_smart/scorm-navigation-fix
 * @copyright 2024 Smart Theme
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define(['jquery'], function($) {
    'use strict';

    var ScormNavigationFix = {
        
        // Configurações
        config: {
            pollingInterval: 2000,  // Intervalo de polling em ms
            maxPollingAttempts: 30, // Máximo de tentativas de polling
            debugMode: false        // Modo debug para logs
        },
        
        // Estado interno
        state: {
            isInitialized: false,
            pollingTimer: null,
            pollingAttempts: 0,
            lastNavigationState: null,
            originalLMSFinish: null,
            originalLMSCommit: null
        },

        /**
         * Inicializa o sistema de correção da navegação SCORM
         */
        init: function() {
            if (this.state.isInitialized) {
                return;
            }

            // Verifica se estamos em uma página SCORM
            if (!this.isScormPage()) {
                return;
            }

            this.log('Inicializando correção da navegação SCORM...');
            
            // Aguarda o carregamento completo do SCORM
            this.waitForScormLoad();
            
            this.state.isInitialized = true;
        },

        /**
         * Verifica se estamos em uma página SCORM
         */
        isScormPage: function() {
            return $('body').hasClass('path-mod-scorm') || 
                   $('#scorm_object').length > 0 ||
                   window.location.href.indexOf('/mod/scorm/') !== -1;
        },

        /**
         * Aguarda o carregamento completo do SCORM antes de aplicar correções
         */
        waitForScormLoad: function() {
            var self = this;
            var attempts = 0;
            var maxAttempts = 50;
            
            var checkInterval = setInterval(function() {
                attempts++;
                
                if (self.isScormLoaded() || attempts >= maxAttempts) {
                    clearInterval(checkInterval);
                    if (self.isScormLoaded()) {
                        self.log('SCORM carregado, aplicando correções...');
                        self.setupNavigationFix();
                    } else {
                        self.log('Timeout aguardando carregamento do SCORM');
                    }
                }
            }, 200);
        },

        /**
         * Verifica se o SCORM foi carregado completamente
         */
        isScormLoaded: function() {
            return (typeof window.API !== 'undefined' || 
                    typeof window.API_1484_11 !== 'undefined') &&
                   (typeof scorm_fixnav === 'function') &&
                   (typeof scorm_buttons !== 'undefined');
        },

        /**
         * Configura as correções da navegação
         */
        setupNavigationFix: function() {
            this.interceptScormAPI();
            this.startNavigationPolling();
            this.setupCompletionListener();
        },

        /**
         * Intercepta as chamadas da API SCORM para detectar conclusão
         */
        interceptScormAPI: function() {
            var self = this;
            
            // Intercepta LMSFinish (SCORM 1.2)
            if (window.API && typeof window.API.LMSFinish === 'function') {
                this.state.originalLMSFinish = window.API.LMSFinish;
                window.API.LMSFinish = function(param) {
                    var result = self.state.originalLMSFinish.call(this, param);
                    self.log('LMSFinish interceptado, forçando atualização da navegação');
                    self.forceNavigationUpdate();
                    return result;
                };
            }

            // Intercepta LMSCommit (SCORM 1.2)
            if (window.API && typeof window.API.LMSCommit === 'function') {
                this.state.originalLMSCommit = window.API.LMSCommit;
                window.API.LMSCommit = function(param) {
                    var result = self.state.originalLMSCommit.call(this, param);
                    self.log('LMSCommit interceptado, forçando atualização da navegação');
                    self.forceNavigationUpdate();
                    return result;
                };
            }

            // Intercepta Terminate (SCORM 2004)
            if (window.API_1484_11 && typeof window.API_1484_11.Terminate === 'function') {
                var originalTerminate = window.API_1484_11.Terminate;
                window.API_1484_11.Terminate = function(param) {
                    var result = originalTerminate.call(this, param);
                    self.log('Terminate interceptado, forçando atualização da navegação');
                    self.forceNavigationUpdate();
                    return result;
                };
            }

            // Intercepta Commit (SCORM 2004)
            if (window.API_1484_11 && typeof window.API_1484_11.Commit === 'function') {
                var originalCommit = window.API_1484_11.Commit;
                window.API_1484_11.Commit = function(param) {
                    var result = originalCommit.call(this, param);
                    self.log('Commit interceptado, forçando atualização da navegação');
                    self.forceNavigationUpdate();
                    return result;
                };
            }
        },

        /**
         * Força a atualização da navegação
         */
        forceNavigationUpdate: function() {
            var self = this;
            
            // Aguarda um pouco para garantir que os dados foram processados
            setTimeout(function() {
                if (typeof scorm_fixnav === 'function') {
                    self.log('Executando scorm_fixnav()...');
                    scorm_fixnav();
                }
                
                // Força atualização adicional após mais tempo
                setTimeout(function() {
                    if (typeof scorm_fixnav === 'function') {
                        scorm_fixnav();
                    }
                }, 1000);
            }, 500);
        },

        /**
         * Inicia polling periódico para verificar mudanças na navegação
         */
        startNavigationPolling: function() {
            var self = this;
            
            this.state.pollingTimer = setInterval(function() {
                self.checkNavigationState();
            }, this.config.pollingInterval);
        },

        /**
         * Verifica o estado atual da navegação e força atualização se necessário
         */
        checkNavigationState: function() {
            if (this.state.pollingAttempts >= this.config.maxPollingAttempts) {
                this.stopPolling();
                return;
            }
            
            this.state.pollingAttempts++;
            
            var currentState = this.getNavigationState();
            
            if (currentState && this.hasNavigationChanged(currentState)) {
                this.log('Mudança detectada na navegação, atualizando...');
                this.forceNavigationUpdate();
                this.state.lastNavigationState = currentState;
            }
        },

        /**
         * Obtém o estado atual da navegação
         */
        getNavigationState: function() {
            var state = {};
            
            if (typeof scorm_buttons !== 'undefined' && scorm_buttons.length > 0) {
                state.buttons = [];
                for (var i = 0; i < scorm_buttons.length; i++) {
                    if (scorm_buttons[i] && typeof scorm_buttons[i].get === 'function') {
                        state.buttons[i] = {
                            disabled: scorm_buttons[i].get('disabled')
                        };
                    }
                }
            }
            
            return state;
        },

        /**
         * Verifica se o estado da navegação mudou
         */
        hasNavigationChanged: function(currentState) {
            if (!this.state.lastNavigationState) {
                return true;
            }
            
            return JSON.stringify(currentState) !== JSON.stringify(this.state.lastNavigationState);
        },

        /**
         * Configura listener para eventos de conclusão
         */
        setupCompletionListener: function() {
            var self = this;
            
            // Monitora mudanças no iframe do SCORM
            var scormObject = $('#scorm_object');
            if (scormObject.length > 0) {
                scormObject.on('load', function() {
                    self.log('SCORM object recarregado, verificando navegação...');
                    setTimeout(function() {
                        self.forceNavigationUpdate();
                    }, 1000);
                });
            }
        },

        /**
         * Para o polling
         */
        stopPolling: function() {
            if (this.state.pollingTimer) {
                clearInterval(this.state.pollingTimer);
                this.state.pollingTimer = null;
                this.log('Polling da navegação interrompido');
            }
        },

        /**
         * Log de debug
         */
        log: function(message) {
            if (this.config.debugMode && console && console.log) {
                console.log('[SCORM Navigation Fix] ' + message);
            }
        }
    };

    return {
        init: function() {
            ScormNavigationFix.init();
        }
    };
});
