<?php

namespace local_degreed_integration\degreed\entities\content;

class video extends abstract_content {

    public string  $url;
    public ?bool   $obsolete       = null;
    public ?string $image_url      = null;
    public ?string $language       = null;
    public ?float  $duration       = null;
    public ?string $duration_type  = null;
    public ?string $publish_date   = null;
    public ?string $format         = null;
    public ?string $owner_id       = null;
    public ?string $owner_type     = null;

    public function get_type() : string {
        return 'Video';
    }

    public function get_api_resource_name() : string {
        return 'videos';
    }

    protected function define_to_api_attributes() : array {
        $attrs = [
            'external-id'   => $this->external_id,
            'title'         => $this->title,
            'summary'       => $this->summary,
            'url'           => $this->url,
            'obsolete'      => $this->obsolete,
            'image-url'     => $this->image_url,
            'language'      => $this->language,
            'duration'      => $this->duration,
            'duration-type' => $this->duration_type,
            'publish-date'  => $this->publish_date,
            'format'        => $this->format,
            'owner-id'      => $this->owner_id,
            'owner-type'    => $this->owner_type,
        ];

        return $attrs;
    }

    protected static function from_api_data(array $data) : static {
        $attributes = $data['attributes'];

        $instance = new static([
            'id'            => $data['id'],
            'external_id'   => $attributes['external-id'],
            'title'         => $attributes['title']        ?? null,
            'summary'       => $attributes['summary']      ?? null,
            'url'           => $attributes['url']          ?? null,
            'obsolete'      => $attributes['obsolete']     ?? null,
            'image_url'     => $attributes['image-url']    ?? null,
            'language'      => $attributes['language']     ?? null,
            'duration'      => isset($attributes['duration']) ? floatval($attributes['duration']) : null,
            'duration_type' => $attributes['duration-type'] ?? null,
            'publish_date'  => $attributes['publish-date'] ?? null,
            'format'        => $attributes['format']       ?? null,
            'owner_id'      => $attributes['owner-id']     ?? null,
            'owner_type'    => $attributes['owner-type']   ?? null,
        ]);

        return $instance;
    }
}
