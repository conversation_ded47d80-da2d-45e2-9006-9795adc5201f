<?php namespace local_degreed_integration\degreed\entities\completion;

use coding_exception;
use \local_degreed_integration\degreed\entities\completion\completion;
use \local_degreed_integration\models\course_sync_record;
use \local_degreed_integration\models\course_completion_sync_record;

class course_completion extends completion {

    const CONTENT_TYPE = 'Course';
    const CONTENTY_ID_TYPE_ID = 'Id';

    public static function from_course_completion_sync_record(course_completion_sync_record $sync_record) : static {
        $user = static::get_user_identifier($sync_record->get('userid'));

        return new static([
            'id' => $sync_record->get('id') ?: null,
            'user_id' => $user->id,
            'user_identifier_type' => $user->type,
            'content_id' => $sync_record->get_course_externalid(),
            'content_id_type' => static::CONTENTY_ID_TYPE_ID,
            'content_type' => static::CONTENT_TYPE,
            'completed_at' => self::format_date_from_timestamp($sync_record->get('timecompleted')),
            'is_verified' => true,
        ]);
    }
}
