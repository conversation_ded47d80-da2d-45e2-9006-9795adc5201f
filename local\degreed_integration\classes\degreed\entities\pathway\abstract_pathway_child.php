<?php

namespace local_degreed_integration\degreed\entities\pathway;

use \local_degreed_integration\degreed\interfaces\embeddable_degreed_entity_interface;
use \local_degreed_integration\degreed\traits\degreed_date_trait;
use \local_degreed_integration\degreed\traits\degreed_duration_trait;

abstract class abstract_pathway_child implements embeddable_degreed_entity_interface {

    public ?string $id;
    public int $sequence = 0;
    public string $title;
    public ?string $description = null;

    protected function __construct(array|object $data = []){
        if(is_object($data)){
            $data = (array) $data;
        }

        foreach ($data as $key => $value) {
            if(property_exists($this, $key)){
                $this->$key = $value;
            }
        }
    }

    /**
     * JSON following the structure
     * {
     *      "data" : {
     *          "attributes" : {...}
*           }
     * }
     *
     * @return string
     */
    public function to_api() : string {
        $data = [
            'data' => [
                'attributes' => [
                    'title' => $this->title,
                    'sequence' => $this->sequence,
                    'description' => $this->description,
                ],
            ]
        ];

        return json_encode($data);
    }

    public static function from_api(string $response) : static {
        $response = json_decode($response, true);

        if(empty($response['data']) || empty($response['data']['attributes'])){
            throw new \InvalidArgumentException("Malformed Degreed's API Response");
        }

        $attributes = $response['data']['attributes'];

        $instance = new static([
            'id' => $response['data']['id'],
            'sequence' => $attributes['sequence'] ?? 0,
            'title' => $attributes['title'] ?? null,
            'description' => $attributes['description'] ?? null,
        ]);

        return $instance;
    }

    abstract public static function from_api_embed(array $data) : static;

    public function to_api_embed() : array {
        $data = [
            'title' => $this->title,
            'sequence' => $this->sequence,
            'description' => $this->description,
        ];

        return $data;
    }


    public static function from_multiple_api_embeds(?array $embeddings) : ?array {
        if($embeddings === null){
            return null;
        }
        return array_map(fn($embed) => static::from_api_embed($embed), $embeddings);
    }
}
