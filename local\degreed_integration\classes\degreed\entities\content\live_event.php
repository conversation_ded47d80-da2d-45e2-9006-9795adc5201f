<?php

namespace local_degreed_integration\degreed\entities\content;

class live_event extends abstract_content {

    public string  $location_type; // 'InPerson', 'Hybrid', or 'Online'.
    public ?string $registration_url   = null;
    public ?string $location_url       = null;
    public ?string $location_address   = null;
    public string  $utc_start_date_time;
    public string  $utc_end_date_time;
    public ?bool   $is_active          = null;
    public ?string $time_zone          = null;

    public function get_type() : string {
        return 'Live Event';
    }

    public function get_api_resource_name() : string {
        return 'relationships/sessions';
    }

    protected function define_to_api_attributes() : array {
        return [
            'external-id'           => $this->external_id,
            'location-type'         => $this->location_type,
            'registration-url'      => $this->registration_url,
            'location-url'          => $this->location_url,
            'location-address'      => $this->location_address,
            'utc-start-date-time'   => $this->utc_start_date_time,
            'utc-end-date-time'     => $this->utc_end_date_time,
            'is-active'             => $this->is_active,
            'time-zone'             => $this->time_zone,
        ];
    }

    protected static function from_api_data(array $data) : static {
        $attributes = $data['attributes'];

        $instance = new static([
            'id'                    => $data['id'],
            'external_id'           => $attributes['external-id'],
            'location_type'         => $attributes['location-type'],
            'registration_url'      => $attributes['registration-url']    ?? null,
            'location_url'          => $attributes['location-url']         ?? null,
            'location_address'      => $attributes['location-address']     ?? null,
            'utc_start_date_time'   => $attributes['utc-start-date-time'],
            'utc_end_date_time'     => $attributes['utc-end-date-time'],
            'is_active'             => $attributes['is-active']           ?? null,
            'time_zone'             => $attributes['time-zone']           ?? null,
        ]);

        return $instance;
    }
}
