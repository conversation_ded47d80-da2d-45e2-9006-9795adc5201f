<?php

namespace local_degreed_integration\degreed\entities\pathway;

class pathway_lesson extends abstract_pathway_child {

    public ?array $resources = null;

    protected function __construct(array|object $data = []){
        if(is_object($data)){
            $data = (array) $data;
        }

        if(!empty($data['resources'])){
            $this->resources = static::from_multiple_api_embeds($data['resources']);
            unset($data['resources']);
        }

        parent::__construct($data);
    }

    /**
     * JSON following the structure
     * {
     *      "data" : {
     *          "attributes" : {...}
*           }
     * }
     *
     * @return string
     */
    public function to_api() : string {
        $data = parent::to_api();

        if(!empty($this->resources)){
            $data['data']['attributes']['resources'] = $this->resources_to_api_embed();
        }

        return json_encode($data);
    }

    public static function from_api(string $response) : static {
        $response = json_decode($response, true);

        if(empty($response['data']) || empty($response['data']['attributes'])){
            throw new \InvalidArgumentException("Malformed Degreed's API Response");
        }

        $attributes = $response['data']['attributes'];

        $instance = new static([
            'id' => $response['data']['id'],
            'sequence' => $attributes['sequence'] ?? 0,
            'title' => $attributes['title'] ?? null,
            'description' => $attributes['description'] ?? null,
            'resources' => $attributes['resources'] ?? null,
        ]);

        return $instance;
    }

    public static function from_api_embed(array $data) : static {
        $instance = new static([
            'id' => $data['lesson-id'],
            'sequence' => $data['sequence'] ?? 0,
            'title' => $data['title'] ?? null,
            'description' => $data['description'] ?? null,
            'resources' => $data['resources'] ?? null,
        ]);

        return $instance;
    }

    public function to_api_embed() : array {
        $data = parent::to_api_embed();

        if(!empty($this->resources)){
            $data['resources'] = $this->resources_to_api_embed();
        }

        return $data;
    }

    protected function resources_to_api_embed() : array {
        $resources = [];

        foreach ($this->resources ?? [] as $resource) {
            $resources[] = $resource->to_api_embed();
        }

        return $resources;
    }
}
