<?php namespace local_degreed_integration\degreed\interfaces;

interface embeddable_degreed_entity_interface extends degreed_entity_interface {

    /**
     * Converts this class into an array that
     * can be embedded and sent to the Degreed API.
     *
     * @return array
     */
    public function to_api_embed() : array;

    /**
     * Instanciates a new object from data that was embedded
     * in a API response.
     *
     * @param array $data
     * @return static
     */
    public static function from_api_embed(array $data) : static;

    /**
     * Same as from_api_embed, but for multiple entities
     *
     * @param array $embeddings
     * @return ?array
     */
    public static function from_multiple_api_embeds(?array $embeddings) : ?array;

}
