<?php namespace local_degreed_integration\degreed\entities\completion;

use \local_degreed_integration\degreed\interfaces\degreed_entity_interface;
use \local_degreed_integration\degreed\traits\degreed_date_trait;
use \local_degreed_integration\degreed\traits\degreed_user_trait;
use \local_degreed_integration\models\course_completion_sync_record;

abstract class completion implements degreed_entity_interface {

    use degreed_date_trait, degreed_user_trait;

    const CONTENT_TYPE = null;

    public string $id;
    public string $user_id;
    public string $user_identifier_type;
    public string $content_id;
    public string $content_id_type;
    public ?string $content_type = null;
    public string $completed_at;
    public ?bool $is_verified = true;
    public ?int $questions_correct = 0;
    public ?float $percentile = 0.0;

    protected ?int $instance_id;
    
    protected function __construct(array|object $data = [])
    {
        if(is_object($data)){
            $data = (array) $data;
        }

        foreach ($data as $key => $value) {
            if(property_exists($this, $key)){
                $this->$key = $value;
            }
        }
    }


    /**
     * JSON following the structure
     * {
     *      "data" : {
     *          "type": "completions",
     *          "attributes" : {...}
     *      }
     * }
     *
     * @return string
     */
    public function to_api() : string {
        return json_encode([
            'data' => [
                'type' => 'completions',
                'attributes' => [
                    'user-id' => $this->user_id,
                    'user-identifier-type' => $this->user_identifier_type,
                    'content-id' => $this->content_id,
                    'content-id-type' => $this->content_id_type,
                    'content-type' => $this->content_type,
                    'completed-at' => $this->completed_at,
                    'is-verified' => $this->is_verified,
                    'questions-correct' => $this->questions_correct,
                    'percentile' => $this->percentile
                ]
            ]
        ]);
    }

    public static function from_api(string $response) : static {
        $response = json_decode($response, true);

        if (empty($response['data']) || empty($response['data']['attributes'])) {
            throw new \InvalidArgumentException("Malformed Degreed's API Response");
        }

        $attributes = $response['data']['attributes'];

        $instance = new static();
        $instance->id = $response['data']['id'];
        $instance->user_id = $attributes['user-id'];
        $instance->user_identifier_type = $attributes['user-identifier-type'];
        $instance->content_id = $attributes['content-id'];
        $instance->content_id_type = $attributes['content-id-type'];
        $instance->content_type = $attributes['content-type'] ?? null;
        $instance->completed_at = $attributes['completed-at'];
        $instance->is_verified = $attributes['is-verified'] ?? true;
        $instance->questions_correct = $attributes['questions-correct'] ?? 0;
        $instance->percentile = $attributes['percentile'] ?? 0.0;

        return $instance;
    }
}
