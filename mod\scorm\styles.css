.path-mod-scorm .top {
    vertical-align: top;
}

.path-mod-scorm .scorm-left {
    text-align: left;
}

.path-mod-scorm .scorm-right {
    text-align: right;
}

.path-mod-scorm .scoframe {
    position: relative;
    width: 100%;
    height: 100%;
}

.ios #scormpage #scorm_content {
    -webkit-overflow-scrolling: touch;
    overflow: scroll;
}

#page-mod-scorm-player #scormtop {
    position: relative;
    width: 100%;
    height: 30px;
}

#page-mod-scorm-player #scormbrowse {
    position: absolute;
    left: 5px;
    top: 0;
}

#page-mod-scorm-player #scormnav {
    position: absolute;
    right: 5px;
    text-align: center;
    top: 3px;
    width: 100%;
}

#page-mod-scorm-player #scormbox {
    width: 74%;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
}

#page-mod-scorm-player #scormpage {
    position: relative;
    width: 100%;
    height: 100%;
}

#page-mod-scorm-player #scormpage #toctree {
    position: relative;
    width: 100%;
}

#page-mod-scorm-player #tocbox {
    position: relative;
    left: 0;
    width: 100%;
    height: 100%;
    font-size: 0.8em;
}

#page-mod-scorm-player #toctree {
    overflow: visible;
}

#page-mod-scorm-player #tochead {
    position: relative;
    text-align: center;
    top: 3px;
    height: 30px;
}

#page-mod-scorm-player #scormpage .scoframe {
    border: 0;
}

#page-mod-scorm-player #scormpage #scorm_object {
    border: none;
    width: 98%;
    height: 98%;
}

#page-mod-scorm-player #scormpage #scorm_object.scorm_nav_under_content {
    height: 95%;
}

#page-mod-scorm-player #scormpage #scorm_content {
    height: 100%;
}

#page-mod-scorm-player #scormpage #scorm_toc {
    position: relative;
}

#page-mod-scorm-player #scormpage #scorm_toc_title {
    font-size: 1.2em;
    font-weight: bold;
}

#page-mod-scorm-player #scormpage #scorm_tree {
    border-right: 5px solid rgb(239, 245, 255);
}

#page-mod-scorm-player #scormpage #scorm_navpanel {
    text-align: center;
}


/* SCORM Header Navigation Styles - Alinhado com o tema Smart
 * Utiliza variáveis CSS customizadas (--primary) para integração com multithemes
 * Suporte completo para tema escuro e responsividade
 * Segue padrões de acessibilidade WCAG 2.1
 */
.scorm-header-navigation {
    position: fixed;
    right: 100px;
    transform: translateX(-50%);
    z-index: 9999;
    border-radius: 0 0 12px 12px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.scorm-header-navigation #scorm_nav {
    display: flex;
    align-items: center;
    gap: 16px;
}

.scorm-header-navigation .scorm-nav-btn {
    background: var(--primary, #007bff);
    color: #ffffff;
    border: 1px solid var(--primary, #007bff);
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 400;
    font-family: "Helvetica Neue", sans-serif;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    user-select: none;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    min-width: 140px;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
}

.scorm-header-navigation .scorm-nav-btn:hover {
    background: var(--primary, #007bff);
    border-color: var(--primary, #007bff);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.25);
    filter: brightness(0.85);
}

.scorm-header-navigation .scorm-nav-btn:focus {
    outline: 0.2rem solid var(--primary, #007bff);
    outline-offset: 2px;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.scorm-header-navigation .scorm-nav-btn:active {
    background: var(--primary, #007bff);
    border-color: var(--primary, #007bff);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    transform: translateY(0);
    filter: brightness(0.7);
}

.scorm-header-navigation .scorm-nav-btn:disabled {
    background: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.65;
}

.scorm-header-navigation .scorm-nav-prev {
    background: #6c757d;
    border-color: #6c757d;
}

.scorm-header-navigation .scorm-nav-prev:hover:not(:disabled) {
    background: #545b62;
    border-color: #4e555b;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.25);
}

.scorm-header-navigation .scorm-nav-prev:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}

.scorm-header-navigation .scorm-nav-prev:active:not(:disabled) {
    background: #4e555b;
    border-color: #47525d;
}

.scorm-header-navigation .scorm-nav-next {
    background: #28a745;
    border-color: #28a745;
}

.scorm-header-navigation .scorm-nav-next:hover:not(:disabled) {
    background: #218838;
    border-color: #1e7e34;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);
}

.scorm-header-navigation .scorm-nav-next:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.scorm-header-navigation .scorm-nav-next:active:not(:disabled) {
    background: #1e7e34;
    border-color: #1c7430;
}

/* Tema escuro - Estilos para modo dark */
[data-theme="dark"] .scorm-header-navigation {
    background: rgba(52, 58, 64, 0.95);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-btn {
    background: var(--primary, #007bff);
    color: #ffffff;
    border-color: var(--primary, #007bff);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-btn:hover {
    background: var(--primary, #007bff);
    border-color: var(--primary, #007bff);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
    filter: brightness(0.85);
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-btn:focus {
    outline-color: var(--primary, #66b3ff);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.4);
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-btn:disabled {
    background: #495057;
    border-color: #495057;
    color: #adb5bd;
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-prev {
    background: #495057;
    border-color: #495057;
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-prev:hover:not(:disabled) {
    background: #343a40;
    border-color: #343a40;
    box-shadow: 0 4px 12px rgba(73, 80, 87, 0.4);
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-prev:focus {
    box-shadow: 0 0 0 0.2rem rgba(73, 80, 87, 0.4);
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-next {
    background: #28a745;
    border-color: #28a745;
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-next:hover:not(:disabled) {
    background: #218838;
    border-color: #1e7e34;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-next:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.4);
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .scorm-header-navigation {
        left: 10px;
        right: 10px;
        width: auto;
        padding: 10px 16px;
    }

    .scorm-header-navigation #scorm_nav {
        gap: 12px;
    }

    .scorm-header-navigation .scorm-nav-btn {
        min-width: 120px;
        padding: 0.375rem 0.75rem;
        font-size: 0.8125rem;
    }
}

/* Melhorias de acessibilidade */
.scorm-header-navigation .scorm-nav-btn:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

[data-theme="dark"] .scorm-header-navigation .scorm-nav-btn:focus-visible {
    outline-color: #66b3ff;
}

/* SMART THEME: Correções para atualização automática da navegação SCORM */
.scorm-header-navigation .scorm-nav-btn.updating {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.scorm-header-navigation .scorm-nav-btn:not(.updating) {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* Indicador visual de carregamento para navegação SCORM */
.scorm-navigation-updating::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: scorm-nav-spin 1s linear infinite;
}

@keyframes scorm-nav-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animações suaves */
@media (prefers-reduced-motion: no-preference) {
    .scorm-header-navigation .scorm-nav-btn {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .scorm-header-navigation .scorm-nav-btn:hover {
        transform: translateY(-2px);
    }

    .scorm-header-navigation .scorm-nav-btn:active {
        transform: translateY(0);
        transition-duration: 0.1s;
    }
}

#page-mod-scorm-player .toc,
#page-mod-scorm-player .no-toc {
    width: 100%;
}

#page-mod-scorm-player .structlist {
    list-style-type: none;
    white-space: nowrap;
}

#page-mod-scorm-player .structurelist {
    position: relative;
    list-style-type: none;
    width: 96%;
    margin: 0;
    padding: 0;
}

#page-mod-scorm-player .structurelist ul {
    padding-left: 0.5em;
    margin-left: 0.5em;
}

#page-mod-scorm-player #scormpage #scorm_toc.disabled,
#page-mod-scorm-player #scormpage #scorm_toc.loading,
#page-mod-scorm-player #scormpage #scorm_toc_toggle.disabled,
#page-mod-scorm-player #scormpage #scorm_toc_toggle.loading {
    display: none;
}

#page-mod-scorm-view .structurelist {
    list-style-type: none;
    white-space: nowrap;
}

#page-mod-scorm-view .structurelist {
    list-style-type: none;
    white-space: nowrap;
}

#page-mod-scorm-view .exceededmaxattempts {
    color: #c00;
}

#page-mod-scorm-player #altfinishlink {
    font-size: 140%;
    border: 0;
    padding: 0;
}

#page-mod-scorm-player #scormmode {
    float: left;
    border: 0;
}

#page-mod-scorm-player.pagelayout-popup #page-content .region-content {
    padding: 0;
}

#page-mod-scorm-player.pagelayout-popup #page-wrapper {
    width: 100%;
}

#page-mod-scorm-player .yui-layout-scroll div.yui-layout-bd {
    overflow: visible;
}

#page-mod-scorm-player .yui-layout-unit-left div.yui-layout-bd {
    overflow: auto;
}

.path-mod-scorm.forcejavascript .toc {
    display: none;
}

.path-mod-scorm.forcejavascript #scormpage #tocbox {
    display: none;
}

.path-mod-scorm.jsenabled .forcejavascriptmessage {
    display: none;
}

.path-mod-scorm.jsenabled .toc {
    display: block;
}

.path-mod-scorm.jsenabled #scormpage #tocbox {
    display: block;
}

#page-mod-scorm-report-userreporttracks table .c1 {
    word-wrap: break-word;
    word-break: break-all;
}

#page-mod-scorm-report .scormattemptcounts {
    clear: left;
    text-align: center;
    display: inline;
    margin-left: 20%;
}

#page-mod-scorm-player #scormpage span.yui3-treeview-icon {
    display: none;
}

#page-mod-scorm-player #scormpage li.yui3-treeview-has-children > div.yui3-treeview-row > span.yui3-treeview-icon {
    display: block;
}

#page-mod-scorm-player #scormpage div.yui3-u-1,
#page-mod-scorm-player #scormpage div.yui3-u-3-4,
#page-mod-scorm-player #scormpage div.yui3-u-1-5,
#page-mod-scorm-player #scormpage div.yui3-u-1-24 {
    display: inline-block;
    *display: inline; /* stylelint-disable-line */
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto;
}

#page-mod-scorm-player #scormpage div.yui3-u-1 {
    display: block;
}

#page-mod-scorm-player #scormpage div.yui3-u-3-4 {
    width: 75%;
}

#page-mod-scorm-player #scormpage div.yui3-u-1-5 {
    width: 20%;
}

#page-mod-scorm-player #scormpage div.yui3-u-1-24 {
    width: 4.1666%;
}

#page-mod-scorm-player #scormpage div.yui3-g-r {
    letter-spacing: normal;
    word-spacing: -0.43em;
}

#scorm_layout {
    margin-bottom: 50px;
}

/**
* Opera as of 12 on Windows needs word-spacing.
* The ".opera-only" selector is used to prevent actual prefocus styling
* and is not required in markup.
*/
#page-mod-scorm-player .opera-only :-o-prefocus,
#page-mod-scorm-player #scormpage div.yui3-g-r img {
    max-width: 100%;
}
