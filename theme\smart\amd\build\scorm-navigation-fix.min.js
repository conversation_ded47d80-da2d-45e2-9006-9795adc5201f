define(["jquery"],function(a){"use strict";var b={config:{pollingInterval:2e3,maxPollingAttempts:30,debugMode:!1},state:{isInitialized:!1,pollingTimer:null,pollingAttempts:0,lastNavigationState:null,originalLMSFinish:null,originalLMSCommit:null},init:function(){this.state.isInitialized||(this.isScormPage()&&(this.log("Inicializando correção da navegação SCORM..."),this.waitForScormLoad(),this.state.isInitialized=!0))},isScormPage:function(){return a("body").hasClass("path-mod-scorm")||a("#scorm_object").length>0||window.location.href.indexOf("/mod/scorm/")!==-1},waitForScormLoad:function(){var a=this,b=0,c=50,d=setInterval(function(){b++,(a.isScormLoaded()||b>=c)&&(clearInterval(d),a.isScormLoaded()?(a.log("SCORM carregado, aplicando correções..."),a.setupNavigationFix()):a.log("Timeout aguardando carregamento do SCORM"))},200)},isScormLoaded:function(){return("undefined"!=typeof window.API||"undefined"!=typeof window.API_1484_11)&&"function"==typeof scorm_fixnav&&"undefined"!=typeof scorm_buttons},setupNavigationFix:function(){this.interceptScormAPI(),this.startNavigationPolling(),this.setupCompletionListener()},interceptScormAPI:function(){var a=this;window.API&&"function"==typeof window.API.LMSFinish&&(this.state.originalLMSFinish=window.API.LMSFinish,window.API.LMSFinish=function(b){var c=a.state.originalLMSFinish.call(this,b);return a.log("LMSFinish interceptado, forçando atualização da navegação"),a.forceNavigationUpdate(),c}),window.API&&"function"==typeof window.API.LMSCommit&&(this.state.originalLMSCommit=window.API.LMSCommit,window.API.LMSCommit=function(b){var c=a.state.originalLMSCommit.call(this,b);return a.log("LMSCommit interceptado, forçando atualização da navegação"),a.forceNavigationUpdate(),c});if(window.API_1484_11&&"function"==typeof window.API_1484_11.Terminate){var b=window.API_1484_11.Terminate;window.API_1484_11.Terminate=function(c){var d=b.call(this,c);return a.log("Terminate interceptado, forçando atualização da navegação"),a.forceNavigationUpdate(),d}}if(window.API_1484_11&&"function"==typeof window.API_1484_11.Commit){var c=window.API_1484_11.Commit;window.API_1484_11.Commit=function(b){var d=c.call(this,b);return a.log("Commit interceptado, forçando atualização da navegação"),a.forceNavigationUpdate(),d}}},forceNavigationUpdate:function(){var a=this;setTimeout(function(){"function"==typeof scorm_fixnav&&(a.log("Executando scorm_fixnav()..."),scorm_fixnav()),setTimeout(function(){"function"==typeof scorm_fixnav&&scorm_fixnav()},1e3)},500)},startNavigationPolling:function(){var a=this;this.state.pollingTimer=setInterval(function(){a.checkNavigationState()},this.config.pollingInterval)},checkNavigationState:function(){if(this.state.pollingAttempts>=this.config.maxPollingAttempts)return void this.stopPolling();this.state.pollingAttempts++;var a=this.getNavigationState();a&&this.hasNavigationChanged(a)&&(this.log("Mudança detectada na navegação, atualizando..."),this.forceNavigationUpdate(),this.state.lastNavigationState=a)},getNavigationState:function(){var a={};if("undefined"!=typeof scorm_buttons&&scorm_buttons.length>0){a.buttons=[];for(var b=0;b<scorm_buttons.length;b++)scorm_buttons[b]&&"function"==typeof scorm_buttons[b].get&&(a.buttons[b]={disabled:scorm_buttons[b].get("disabled")})}return a},hasNavigationChanged:function(a){return this.state.lastNavigationState?JSON.stringify(a)!==JSON.stringify(this.state.lastNavigationState):!0},setupCompletionListener:function(){var b=this,c=a("#scorm_object");c.length>0&&c.on("load",function(){b.log("SCORM object recarregado, verificando navegação..."),setTimeout(function(){b.forceNavigationUpdate()},1e3)})},stopPolling:function(){this.state.pollingTimer&&(clearInterval(this.state.pollingTimer),this.state.pollingTimer=null,this.log("Polling da navegação interrompido"))},log:function(a){this.config.debugMode&&console&&console.log&&console.log("[SCORM Navigation Fix] "+a)}};return{init:function(){b.init()}}});
