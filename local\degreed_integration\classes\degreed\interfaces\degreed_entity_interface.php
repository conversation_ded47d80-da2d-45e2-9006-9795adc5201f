<?php namespace local_degreed_integration\degreed\interfaces;

interface degreed_entity_interface {
    /**
     * Converts the entity into a JSON string formatted for Degreed's API.
     *
     * The structure follows:
     * {
     *     "data": {
     *         "attributes": { ... }
     *     }
     * }
     *
     * @return string JSON representation of the entity for API consumption.
     */
    public function to_api() : string;

    /**
     * Creates an instance of the entity from a Degreed API response.
     *
     * @param string $response The JSON response from the Degreed API.
     * @return static An instance of the entity populated with API data.
     */
    public static function from_api(string $response) : static;

}
