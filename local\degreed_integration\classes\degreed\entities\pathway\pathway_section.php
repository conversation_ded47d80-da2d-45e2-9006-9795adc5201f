<?php

namespace local_degreed_integration\degreed\entities\pathway;

use coding_exception;

class pathway_section extends abstract_pathway_child {

    public ?array $lessons = null;
    protected pathway $pathway;

    protected function __construct(array|object $data = []){
        if(is_object($data)){
            $data = (array) $data;
        }

        if(!empty($data['lessons'])){
            $this->lessons = static::from_multiple_api_embeds($data['lessons']);
            unset($data['lessons']);
        }

        parent::__construct($data);
    }

    public function set_pathway(pathway $pathway) : static {
        $this->pathway = $pathway;
        return $this;
    }

    public function get_pathway() : pathway {
        if(!isset($this->pathway)){
            $this->pathway = $this->fetch_pathway();
        }
        return $this->pathway;
    }

    protected function fetch_pathway() : pathway{
        throw new coding_exception("TODO");
    }

    /**
     * JSON following the structure
     * {
     *      "data" : {
     *          "attributes" : {...}
     *      }
     * }
     *
     * @return string
     */
    public function to_api() : string {
        $data = parent::to_api();

        if(!empty($this->lessons)){
            $data['data']['attributes']['lessons'] = $this->lessons_to_api_embed();
        }

        return json_encode($data);
    }

    public static function from_api(string $response) : static {
        $response = json_decode($response, true);

        if(empty($response['data']) || empty($response['data']['attributes'])){
            throw new \InvalidArgumentException("Malformed Degreed's API Response");
        }

        $attributes = $response['data']['attributes'];

        $instance = new static([
            'id' => $response['data']['id'],
            'sequence' => $attributes['sequence'] ?? 0,
            'title' => $attributes['title'] ?? null,
            'description' => $attributes['description'] ?? null,
            'lessons' => $attributes['lessons'] ?? null,
        ]);

        return $instance;
    }

    public static function from_api_embed(array $data) : static {
        $instance = new static([
            'id' => $data['section-id'],
            'sequence' => $data['sequence'] ?? 0,
            'title' => $data['title'] ?? null,
            'description' => $data['description'] ?? null,
            'lessons' => $data['lessons'] ?? null,
        ]);

        return $instance;
    }

    public function to_api_embed() : array {
        $data = parent::to_api_embed();

        if(!empty($this->lessons)){
            $data['lessons'] = $this->lessons_to_api_embed();
        }

        return $data;
    }

    protected function lessons_to_api_embed() : array {
        $lessons = [];

        foreach ($this->lessons ?? [] as $lesson) {
            $lessons[] = $lesson->to_api_embed();
        }

        return $lessons;
    }

    // public static function from_trail_sync_record(trail_sync_record $sync_record) : static {
    //     if(!$instance = $sync_record->get_course()){
    //         throw new degreed_integration_exception('exception:missing_course');
    //     }

    //     $trail = new static([
    //         'id' => $sync_record->get('externalid') ?: null,
    //         'courseid' => $instance->id,
    //         'title' => $instance->fullname,
    //         'summary' => $instance->summary,
    //         'image_url' => image_helper::get_course_image_url($instance),
    //         'sections' => null, // Change?
    //     ]);

    //     return $trail;
    // }
}
