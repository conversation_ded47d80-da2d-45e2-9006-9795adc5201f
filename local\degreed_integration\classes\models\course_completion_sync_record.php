<?php namespace local_degreed_integration\models;

use \local_degreed_integration\models\entity_sync_record;
use \local_degreed_integration\models\course_sync_record;

use \core\event\course_completed;
use \context_course;
use local_degreed_integration\util\user_helper;

class course_completion_sync_record extends entity_sync_record {

    const TABLE = 'degreed_course_completions';

    protected static function define_properties() {
        $definition = parent::define_properties();

        $definition['courseid'] = [
            'type' => PARAM_INT,
        ];
        $definition['userid'] = [
            'type' => PARAM_INT,
        ];
        $definition['timecompleted'] = [
            'type' => PARAM_INT,
        ];

        return $definition;
    }


    public static function get_pending_records_generator(int $limit = 0) : \Generator {
        global $DB;

        $table = "{".static::TABLE."}";
        $course_record_table = "{".course_sync_record::TABLE."}";

        $sql = "SELECT ccr.*
                FROM $table ccr
                    JOIN $course_record_table cr ON (
                        cr.courseid = ccr.courseid
                        AND cr.enabled = 1
                        AND cr.externalid IS NOT NULL
                    )
                WHERE ccr.timechanged > ccr.timesynchronized
                ORDER BY ccr.timechanged DESC";

        $recordset = $DB->get_recordset_sql($sql, null, 0, $limit);

        foreach ($recordset as $record) {
            yield new static(0, $record);
        }

        $recordset->close();
    }


    /**
     * Retrieves or creates a new instance from the
     * course completion objeect
     *
     * @param object $cc course completion object
     * @return static
     */
    public static function get_or_create_from_course_completion(object $cc) : ?static {
        $raw = ['courseid' => $cc->course, 'userid' => $cc->userid];
        return self::get_record($raw) ?: new static(0, (object) $raw);
    }


    public static function upsert_from_event(course_completed $event) : ?static {
        $course_completion = $event->get_record_snapshot($event->objecttable, $event->objectid);

        if(empty($course_completion->timecompleted)){
            return null;
        }

        if(!user_helper::is_worker($event->relateduserid)){
            return null; // Only "colaboradores" should be integrated
        }

        $record = static::get_or_create_from_course_completion($course_completion);

        $record->set('timecompleted', $course_completion->timecompleted);
        $record->mark_as_changed();
        $record->save();
        
        return $record;
    }

    public function get_course_externalid() : ?string {
        return course_sync_record::get_externalid_from_courseid($this->get('courseid'));
    }

    /**
     * Returns the course context.
     *
     * @return object|null
     */
    public function get_course_context() : ?context_course {
        return context_course::instance($this->get('courseid'), IGNORE_MISSING) ?: null;
    }
}
