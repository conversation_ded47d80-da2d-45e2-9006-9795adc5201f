<?php

require_once(__DIR__ . '/../../config.php');


// $client = new \local_degreed_integration\degreed\degreed_client();
// $client->login();
// var_dump($client);
// var_dump($client->get_course('rnp1zY3'));
// var_dump($client->logout());

// $course = (object)[
//     'id' => 2,
// ];

// \tool_lfxp\helpers\custom_fields\course\custom_course_field::load_custom_fields($course);
// var_dump($course);

// $value = \tool_lfxp\helpers\custom_fields\course\custom_course_field::get_field_value('cadastrar_na_degreed', 2);
// var_dump($value->export_value());
