<?php

namespace local_degreed_integration\degreed\entities\content;

use local_degreed_integration\degreed\entities\abstract_entity;

abstract class abstract_content extends abstract_entity {

    public string $external_id;
    public ?string $title = null;
    public ?string $summary = null;

    /**
     * Article, Book, Course, Event, Podcast, Task, Video or Assessment
     *
     * @return string
     */
    abstract public function get_type() : string;

    /**
     * articles, books, courses, events, podcasts, tasks, videos or assessments
     *
     * @return string
     */
    abstract public function get_api_resource_name() : string;

    public function to_api() : string {
        return json_encode([
            'data' => [
                'type' => 'content/' . $this->get_api_resource_name(),
                'attributes' => $this->define_to_api_attributes(),
            ]
        ]);
    }

}
