<?php

use profilefield_company\companies;
use tool_companymanagement\repositories\company_repository;
use tool_companymanagement\services\company_service;
use local_ssystem\util\data_formatter_trait;
use tool_companymanagement\util\cnpj_helper;

/**
 * Class profile_field_company
 *
 * @copyright  2024 REVVO <www.somosrevvo.com.br>
 */
class profile_field_company extends profile_field_base
{
    use data_formatter_trait;

    protected company_service $service;

    public function __construct($fieldid=0, $userid=0, $fielddata=null){
        parent::__construct($fieldid, $userid, $fielddata);

        $this->service = new company_service(new company_repository);
    }

    /**
     * Display the saved company names in the user profile.
     *
     * @return string Comma-separated list of company names, or a "none selected" message.
     */
    public function display_data() {
        // Split stored CSV and trim whitespace.
        $cnpjs = array_filter(
            array_map('trim', explode(',', $this->data)),
            fn(string $cnpj) => $cnpj !== ''
        );
        if (empty($cnpjs)) {
            return get_string('nocompanyselected', 'profilefield_company');
        }

        // Fetch all matching companies.
        $companyList = $this->service->get_companies_by_cnpj($cnpjs);

        // Re-index by raw CNPJ for quick lookup.
        $map = [];
        foreach ($companyList as $company) {
            $map[$company->get_raw_cnpj()] = $company->get('name');
        }

        // Build output in original order, ignoring CNPJs not found.
        $names = [];
        foreach ($cnpjs as $cnpj) {
            if (isset($map[$cnpj])) {
                $names[] = $map[$cnpj];
            }
        }

        if (empty($names)) {
            return get_string('nocompanyselected', 'profilefield_company');
        }
        return implode(', ', $names);
    }

    /**
     * Add fields for editing a text profile field.
     * @param moodleform $mform
     */
    public function edit_field_add($mform)
    {
        $attributes = [
            'multiple' => true,
            'noselectionstring' => get_string('nocompanyselected', 'profilefield_company'),
            'ajax' => 'profilefield_company/autocomplete',
        ];

        $selected_companies = [];

        $values = explode(',', $this->data);
        if (!empty($values)) {
            $existing = $this->service->get_companies_by_cnpj($values);
            foreach ($existing as $company) {
                $selected_companies[$company->get_raw_cnpj()] = $company->get('name');
            }
        }

        $mform->addElement(
            'autocomplete',
            $this->inputname,
            format_string($this->field->name),
            $selected_companies,
            $attributes
        );

        $mform->setDefault($this->inputname, $values);

        $mform->setType($this->inputname, PARAM_INT);
    }

    /**
     * Save the submitted CNPJs, but only those that already exist.
     *
     * @param stdClass $usernew Data submitted from the profile form.
     */
    public function edit_save_data($usernew) {
        global $DB;

        if (!isset($usernew->{$this->inputname})) {
            return;
        }

        $raw = $usernew->{$this->inputname};

        if(is_string($raw)){
            $raw = explode(',', $raw);
        }else{
            $raw = (array) $raw;
        }

        $cnpjs = array_filter(array_map('trim', $raw));

        // Retrieve existing companies only.
        $existing = $this->service->get_companies_by_cnpj($cnpjs);

        // Extract raw CNPJ values to save.
        $validated = [];
        foreach ($existing as $company) {
            $validated[] = $company->get_raw_cnpj();
        }

        // Prepare record for user_info_data.
        $record = new stdClass();
        $record->userid  = $usernew->id;
        $record->fieldid = $this->field->id;
        $record->data    = implode(',', $validated);

        // Insert or update the record.
        if ($recid = $DB->get_field(
            'user_info_data', 'id',
            ['userid' => $record->userid, 'fieldid' => $record->fieldid]
        )) {
            $record->id = $recid;
            $DB->update_record('user_info_data', $record);
        } else {
            $DB->insert_record('user_info_data', $record);
        }
    }

    /**
     * Returns the list of companies to be used on the field.
     *
     * @return array
     */
    protected function get_companies_menu(): array
    {
        $companies = companies::get_menu();

        return $companies;
    }

    /**
     * Convert imported value(s) into raw CNPJ string(s).
     * Uses the company service to validate and create missing entries.
     *
     * @param string|array $data  A CNPJ or comma-separated list of CNPJs, or an array of CNPJs.
     * @return string
     */
    public function convert_external_data($data): string {
        return $this->service->validate_cnpjs_and_create_missing_companies($data);
    }
}
