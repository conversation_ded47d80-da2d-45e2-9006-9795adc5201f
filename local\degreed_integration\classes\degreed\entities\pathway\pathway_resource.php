<?php

namespace local_degreed_integration\degreed\entities\pathway;

use \local_degreed_integration\degreed\interfaces\embeddable_degreed_entity_interface;
use \local_degreed_integration\degreed\traits\degreed_date_trait;
use \local_degreed_integration\degreed\traits\degreed_duration_trait;
use local_degreed_integration\models\course_sync_record;

class pathway_resource implements embeddable_degreed_entity_interface {

    public ?string $id; // content-id
    public int $sequence = 0;
    public ?string $requirement = null; // optional or required
    public ?string $type = null; // Article, Book, Course, Event, Podcast, Task, Video or Assessment
    public ?string $note = null;

    protected function __construct(array|object $data = []){
        if(is_object($data)){
            $data = (array) $data;
        }

        foreach ($data as $key => $value) {
            if(property_exists($this, $key)){
                $this->$key = $value;
            }
        }
    }

    /**
     * JSON following the structure
     * {
     *      "data" : {
     *          "attributes" : {...}
*           }
     * }
     *
     * @return string
     */
    public function to_api() : string {
        $data = [
            'data' => [
                'attributes' => [
                    'type' => $this->type,
                    'id' => $this->id,
                ],
            ]
        ];

        return json_encode($data);
    }

    public static function from_api(string $response) : static {
        $response = json_decode($response, true);

        if(empty($response['data']) || empty($response['data']['attributes'])){
            throw new \InvalidArgumentException("Malformed Degreed's API Response");
        }

        $attributes = $response['data']['attributes'];

        $instance = new static([
            'id' => $response['data']['id'],
            'sequence' => $attributes['sequence'] ?? 0,
            'type' => $attributes['title'] ?? null,
            'note' => $attributes['note'] ?? null,
            'requirement' => $attributes['requirement'] ?? null,
        ]);

        return $instance;
    }

    public static function from_api_embed(array $data) : static {
        $instance = new static([
            'id' => $data['lesson-id'],
            'sequence' => $data['sequence'] ?? 0,
            'type' => $data['type'] ?? null,
            'note' => $data['note'] ?? null,
            'requirement' => $data['requirement'] ?? null,
        ]);

        return $instance;
    }

    public function to_api_embed() : array {
        $data = [
            'id' => $this->id,
            'sequence' => $this->sequence,
            'type' => $this->type,
            'note' => $this->note,
            'requirement' => $this->requirement,
        ];

        return $data;
    }


    public static function from_multiple_api_embeds(?array $embeddings) : ?array {
        if($embeddings === null){
            return null;
        }
        return array_map(fn($embed) => static::from_api_embed($embed), $embeddings);
    }

    public static function from_course_sync_record(course_sync_record $record) : static {
        return new static([
            'id' => $record->get('externalid') ?: null,
            'type' => 'Course',
        ]);
    }
}
