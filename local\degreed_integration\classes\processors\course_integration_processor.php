<?php namespace local_degreed_integration\processors;

global $CFG;

require_once( $CFG->dirroot . '/local/customfields/tests/traits/local_customfields_testcase_trait.php');

use \local_degreed_integration\processors\abstract_integration_processor;
use \local_degreed_integration\degreed\degreed_client;
use \local_degreed_integration\models\course_sync_record;
use \local_degreed_integration\degreed\entities\content\course;
use \local_degreed_integration\event\degreed_course_created;
use \local_degreed_integration\event\degreed_course_updated;
use \local_degreed_integration\exceptions\degreed_integration_exception;

use local_customfields_testcase_trait;
class course_integration_processor extends abstract_integration_processor{

    use local_customfields_testcase_trait;

    public function execute(){
        foreach (course_sync_record::get_pending_records_generator() as $course) {
            $this->sync_course($course);
        }
    }

    protected function sync_course(course_sync_record &$sync_record){
        try {
            if(empty($sync_record->get('externalid'))){
                $this->create_course($sync_record);
            }else{
                $this->update_course($sync_record);
            }
        } catch (\Throwable $th) {
            mtrace($th->getMessage());

            if($th instanceof degreed_integration_exception){
                mtrace($th->get_debug_info());
            }
        }
    }

    protected function create_course(course_sync_record &$sync_record){
        $course = course::from_course_sync_record($sync_record);
        $this->get_client()->create_course($course);

        if(!empty($course->id)){
            $sync_record->set('externalid', $course->id);
            $sync_record->mark_as_synchronized();
            $sync_record->save();

            $event = degreed_course_created::create_from_sync_record($sync_record);
            $event->trigger();
        }

        $this->upsert_course_skills($course);
    }

    protected function update_course(course_sync_record &$sync_record){
        $course = course::from_course_sync_record($sync_record);
        $this->get_client()->update_course($course);
        $this->upsert_course_skills($course);

        $sync_record->mark_as_synchronized();
        $sync_record->save();

        $event = degreed_course_updated::create_from_sync_record($sync_record);
        $event->trigger();
    }

    protected function upsert_course_skills(course $course){
        $this->get_client()->upsert_course_skills($course);
    }
}

