<?php namespace local_degreed_integration;

defined('MOODLE_INTERNAL') || die();

global $CFG;

require_once( $CFG->dirroot . '/local/degreed_integration/tests/fixtures/degreed_test_trait.php');
require_once( $CFG->dirroot . '/local/customfields/tests/traits/local_customfields_testcase_trait.php');

use \DateTime;
use \advanced_testcase;
use \local_degreed_integration\models\course_sync_record;
use \local_degreed_integration\models\course_completion_sync_record;
use \tool_lfxp\helpers\custom_fields\course\custom_course_field;
use \local_degreed_integration\degreed\entities\content\course;
use \local_degreed_integration\degreed\entities\skill;
use \local_degreed_integration\degreed\entities\completion\course_completion;
use \local_ssystem\constants\custom_course_fields;

use degreed_test_trait;
use local_customfields_testcase_trait;
use \local_degreed_integration\degreed\traits\degreed_date_trait;

class degreed_entities_test extends advanced_testcase{

    const DEGREED_DATE_FORMAT = 'Y-m-d\TH:i:s.u\Z';

    use degreed_test_trait;
    use local_customfields_testcase_trait;

    public static function setUpBeforeClass(): void {
        parent::setUpBeforeClass();

        // Adding columns to the customfield tables
        self::update_local_customfields_columns();
    }
    
    /**
     * @group !skip
     * @return void
     */
    public function test_course_entity_from_course_sync_record(){
        global $CFG;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $format = custom_course_fields::get_solution_format_options()[1];
        $skills = ['Test1', 'Test2', 'Test3'];
        $course = $this->getDegreedGenerator()->create_degreed_course([
            'customfields' => [
                [
                    'shortname' => skill::CUSTOM_FIELD_SHORTNAME,
                    'value' => $skills,
                ],
                [
                    'shortname' => course::COURSE_DURATION_FIELD_SHORTNAME,
                    'value' => 7200,
                ],
                [
                    'shortname' => custom_course_fields::COMPLEXITY_LEVEL,
                    'value' => custom_course_fields::COMPLEXITY_INTERMEDIATE,
                ],
                [
                    'shortname' => custom_course_fields::SOLUTION_FORMAT,
                    'value' => $format,
                ],                
            ]
        ]);
        self::add_course_data_to_local_customfields($course); // Correcting broken (in phpunit) observer

        $this->runAdhocTasks();

        $course_sync_record = course_sync_record::get_or_create_from_courseid($course->id);
        $entity = course::from_course_sync_record($course_sync_record);

        // Checking entity
        $this->assertNotEmpty($entity->external_id);
        $this->assertEquals($course->fullname, $entity->title);
        $this->assertNotEmpty($entity->summary);
        $this->assertEquals($CFG->wwwroot . '/course/view.php?id=' . $course->courseid, $entity->url);

        if(!empty($entity->image_url)){
            $this->assertStringStartsWith($CFG->wwwroot . '/local/degreed_integration/pluginfile.php', $entity->image_url);
        }

        if(!empty($entity->publish_date)){
            $this->assertNotFalse(DateTime::createFromFormat(self::DEGREED_DATE_FORMAT, $entity->publish_date));
        }

        $this->assertEquals(2.0, $entity->duration);
        $this->assertEquals('hours', $entity->duration_type);

        $this->assertEquals(custom_course_fields::COMPLEXITY_INTERMEDIATE, $entity->difficulty);

        $this->assertEquals($format, $entity->format);

        // Checking skills
        $entity_skills = $entity->get_skills();
        foreach ($skills as $skill) {
            $this->assertNotFalse(array_search($skill, array_column($entity_skills, 'id')));
        }
    }

    /**
     * @group !skip
     * @return void
     */
    public function test_course_entity_to_api(){
        global $CFG;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $course_reflection = new \ReflectionClass(course::class);
        $course = $course_reflection->newInstanceWithoutConstructor();
        $constructor = $course_reflection->getConstructor();
        $constructor->setAccessible(true);
        $constructor->invoke($course, [
            'external_id' => 'MOCKED_EXTERNAL_ID',
            'title' => "MOCKED_TITLE",
            'summary' => "MOCKED_SUMMARY",
            'publish_date' => '2024-01-08T14:21:01.41Z',
            'url' => $CFG->wwwroot . '/course/view.php?id=1',
            'image_url' => $CFG->wwwroot . '/pluginfile.php/1/course/overviewfiles/card.png?rev=1712345651',
            'duration' => 10,
            'duration_type' => 'minutes',
            'difficulty' => custom_course_fields::COMPLEXITY_BASIC,
        ]);

        $api = json_decode($course->to_api(), true);

        $this->assertNotEmpty($api['data']);

        $api_attributes = $api['data']['attributes'];
        foreach ($this->get_api_to_course_entity_map() as $api_key => $entity_key) {
            $this->assertEquals($course->$entity_key, $api_attributes[$api_key]);
        }
    }

    /**
     * @group !skip
     * @return void
     */
    public function test_api_to_course_entity(){
        global $CFG;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $json = [
            'data' => [
                'id' => "MOCKED_ID",
                'type' => 'content/courses',
                'attributes' => [
                    'external-id' => 'MOCKED_EXTERNAL_ID',
                    'title' => "MOCKED_TITLE",
                    'summary' => "MOCKED_SUMMARY",
                    'publish-date' => '2024-01-08T14:21:01.41Z',
                    'url' => $CFG->wwwroot . '/course/view.php?id=1',
                    'image-url' => $CFG->wwwroot . '/pluginfile.php/1/course/overviewfiles/card.png?rev=1712345651',
                    'duration' => 10,
                    'duration-type' => 'minutes',
                    'difficulty' => custom_course_fields::COMPLEXITY_BASIC,
                    'language' => 'MOCKED_LANGUAGE',
                    'format' => 'MOCKED_FORMAT',
                    'cost-units' => 99,
                    'cost-units-type' => '$',
                    'video-url' => 'https://degreed.com/video/abcd',
                    'obsolete' => false,
                    'owner-id' => 'MOCKED_OWNER_ID',
                    'owner-type' => 'user',
                    'provider-code' => "MOCKED_PROVIDER_CODE",
                    'degreed-url' => "https://degreed.com",
                    'learning-minutes' => 600,
                    'created-at' => '2024-01-08T14:21:01.41Z',
                    'modified-at' => '2024-01-08T14:21:01.41Z',
                    'continuing-education-units' => 123,
                    'is-internal' => false,
                ],
                'links' => [
                    'self' => '...',
                ],
            ],
        ];

        $course = course::from_api(json_encode($json));

        $data = $json['data'];
        $this->assertEquals($data['id'], $course->id);
        foreach ($this->get_api_to_course_entity_map() as $api_key => $entity_key) {
            if(isset($data[$api_key])){
                $this->assertEquals($data['attributes'][$api_key], $course->$entity_key);
            }
        }
    }

    protected function get_api_to_course_entity_map(){
        return [
            'external-id' => 'external_id',
            'title' => 'title',
            'summary' => 'summary',
            'publish-date' => 'publish_date',
            'url' => 'url',
            'obsolete' => 'obsolete',
            'image-url' => 'image_url',
            'language' => 'language',
            'duration' => 'duration',
            'duration-type' => 'duration_type',
            'cost-units' => 'cost_units',
            'cost-unit-type' => 'cost_unit_type',
            'format' => 'format',
            'difficulty' => 'difficulty',
            'video-url' => 'video_url',
            'continuing-education-units' => 'continuing_education_units',
            'owner-id' => 'owner_id',
            'owner-type' => 'owner_type',
            'visibility-groups' => 'visibility_groups',
        ];
    }

    protected function get_api_to_course_completion_entity_map(){
        return [
            'user-id' => 'user_id',
            'user-identifier-type' => 'user_identifier_type',
            'content-id' => 'content_id',
            'content-id-type' => 'content_id_type',
            'content-type' => 'content_type',
            'completed-at' => 'completed_at',
            'is-verified' => 'is_verified',
            'questions-correct' => 'questions_correct',
            'percentile' => 'percentile',
        ];
    }


    /**
     * @group !skip
     * @return void
     */
    public function test_course_completion_entity_from_course_sync_record(){
        global $CFG;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $course = $this->getDegreedGenerator()->create_degreed_course();
        $user = $this->getDataGenerator()->create_user();

        $this->runAdhocTasks();

        $course_sync = course_sync_record::get_or_create_from_courseid($course->id);
        $course_sync->set('externalid', "MOCKED_EXTERNAL_ID");
        $course_sync->save();

        $cc_sync = course_completion_sync_record::get_or_create_from_course_completion((object)[
            'course' => $course->id,
            'userid' => $user->id,
        ]);
        $cc_sync->set('timecompleted', time());
        $cc_sync->mark_as_changed();
        $cc_sync->save();

        $course_completion = course_completion::from_course_completion_sync_record($cc_sync);

        $this->assertNotEmpty($course_completion->id);
        $this->assertNotEmpty($course_completion->user_id);
        $this->assertNotEmpty($course_completion->user_identifier_type);
        $this->assertEquals($course_sync->get('externalid'), $course_completion->content_id);
        $this->assertEquals(course_completion::CONTENTY_ID_TYPE_ID, $course_completion->content_id_type);
        $this->assertEquals(course_completion::CONTENT_TYPE, $course_completion->content_type);
        $this->assertNotFalse(DateTime::createFromFormat(self::DEGREED_DATE_FORMAT, $course_completion->completed_at));
    }

    /**
     * @group !skip
     * @return void
     */
    public function test_api_to_course_completion_entity(){
        global $CFG;

        $this->resetAfterTest(true);
        $this->setAdminUser();

        $json = [
            'data' => [
                'id' => "MOCKED_ID",
                'type' => 'completions',
                'attributes' => [
                    'user-id' => 'MOCKED_USER_ID',
                    'user-identifier-type' => 'EmployeeId',
                    'content-id' => 'MOCKED_CONTENT_ID',
                    'content-id-type' => course_completion::CONTENTY_ID_TYPE_ID,
                    'content-type' => course_completion::CONTENT_TYPE,
                    'completed-at' => '2024-01-08T14:21:01.41Z',
                    'is-verified' => true,
                    'questions-correct' => null,
                    'percentile' => null,
                    'added-at' => '2024-01-08T14:21:01.41Z',
                    'added-at' => '2024-01-08T14:21:01.41Z',
                    'rating' => 1,
                    'access-method' => '??',
                    'provider-id' => "MOCKED_PROVIDER_ID",
                ],
                'links' => [
                    'self' => '...',
                ],
            ],
        ];

        $course_completion = course_completion::from_api(json_encode($json));

        $data = $json['data'];
        $this->assertEquals($data['id'], $course_completion->id);
        foreach ($this->get_api_to_course_completion_entity_map() as $api_key => $entity_key) {
            if(isset($data[$api_key])){
                $this->assertEquals($data['attributes'][$api_key], $course_completion->$entity_key);
            }
        }
    }

    
    /**
     * @group !skip
     * @return void
     */
    public function test_course_completion_entity_to_api(){
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $cc_reflection = new \ReflectionClass(course_completion::class);
        $course_completion = $cc_reflection->newInstanceWithoutConstructor();
        $constructor = $cc_reflection->getConstructor();
        $constructor->setAccessible(true);
        $constructor->invoke($course_completion, [
            'user_id' => 'MOCKED_USER_ID',
            'user_identifier_type' => 'EmployeeId',
            'content_id' => 'MOCKED_CONTENT_ID',
            'content_id_type' => course_completion::CONTENTY_ID_TYPE_ID,
            'content_type' => course_completion::CONTENT_TYPE,
            'completed_at' => '2024-01-08T14:21:01.41Z',
            'is_verified' => true,
            'questions_correct' => null,
            'percentile' => null,
        ]);

        $api = json_decode($course_completion->to_api(), true);

        $this->assertNotEmpty($api['data']);

        $api_attributes = $api['data']['attributes'];
        foreach ($this->get_api_to_course_completion_entity_map() as $api_key => $entity_key) {
            $this->assertEquals($course_completion->$entity_key, $api_attributes[$api_key]);
        }
    }
}
