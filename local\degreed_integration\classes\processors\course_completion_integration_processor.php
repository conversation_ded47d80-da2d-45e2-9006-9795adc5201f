<?php namespace local_degreed_integration\processors;

use \local_degreed_integration\processors\abstract_integration_processor;
use \local_degreed_integration\models\course_completion_sync_record;
use \local_degreed_integration\degreed\entities\completion\course_completion;
use \local_degreed_integration\event\degreed_course_completion_created;
use \local_degreed_integration\event\degreed_course_completion_updated;


class course_completion_integration_processor extends abstract_integration_processor{

    public function execute(){
        foreach (course_completion_sync_record::get_pending_records_generator() as $completion) {
            $this->sync_completion($completion);
        }
    }

    protected function sync_completion(course_completion_sync_record &$sync_record){
        try {
            if(empty($sync_record->get('externalid'))){
                $this->create_completion($sync_record);
            }else{
                $this->update_completion($sync_record);
            }
        } catch (\Throwable $th) {
            mtrace($th->getMessage());
        }
    }

    protected function create_completion(course_completion_sync_record &$sync_record){
        $completion = course_completion::from_course_completion_sync_record($sync_record);
        $this->get_client()->create_course_completion($completion);

        if(!empty($completion->id)){
            $sync_record->set('externalid', $completion->id);
            $sync_record->mark_as_synchronized();
            $sync_record->save();

            $event = degreed_course_completion_created::create_from_sync_record($sync_record);
            $event->trigger();
        }
    }

    protected function update_completion(course_completion_sync_record &$sync_record){
        $completion = course_completion::from_course_completion_sync_record($sync_record);
        $this->get_client()->create_course_completion($completion);

        $sync_record->mark_as_synchronized();
        $sync_record->save();

        $event = degreed_course_completion_updated::create_from_sync_record($sync_record);
        $event->trigger();
    }
}