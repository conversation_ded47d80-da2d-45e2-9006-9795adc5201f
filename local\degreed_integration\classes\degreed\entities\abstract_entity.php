<?php

namespace local_degreed_integration\degreed\entities;

use \local_degreed_integration\degreed\interfaces\degreed_entity_interface;
use \local_degreed_integration\degreed\traits\degreed_date_trait;
use \local_degreed_integration\degreed\traits\degreed_duration_trait;

abstract class abstract_entity implements degreed_entity_interface {

    use degreed_date_trait, degreed_duration_trait;
    
    /** 
     * The unique identifier of the entity in Degreed.
     *
     * @var string|null
     */
    public ?string $id;

    protected function __construct(array|object $data = []){
        if(is_object($data)){
            $data = (array) $data;
        }

        foreach ($data as $key => $value) {
            if(property_exists($this, $key)){
                $this->$key = $value;
            }
        }
    }

    /**
     * Converts the entity into a JSON string formatted for Degreed's API.
     *
     * The structure follows:
     * {
     *     "data": {
     *         "attributes": { ... }
     *     }
     * }
     *
     * @return string JSON representation of the entity for API consumption.
     */
    public function to_api() : string {
        return json_encode([
            'data' => [
                'attributes' => $this->define_to_api_attributes(),
            ]
        ]);
    }

    /**
     * Creates an instance of the entity from a Degreed API response.
     *
     * Validates and parses the JSON string to extract attributes and instantiate the entity.
     *
     * @param string $response The JSON response from the Degreed API.
     * @return static An instance of the entity populated with API data.
     * @throws \InvalidArgumentException If the response is malformed or missing required data.
     */
    public static function from_api(string $response) : static {
        $response = json_decode($response, true);

        if(empty($response['data']) || empty($response['data']['attributes'])){
            throw new \InvalidArgumentException("Malformed Degreed's API Response");
        }

        $data = $response['data'];
        return static::from_api_data($data);
    }

    /**
     * Defines the attributes to be sent to Degreed's API.
     *
     * This method must be implemented by the concrete entity to specify
     * which data should be included in the 'attributes' section of the API payload.
     *
     * @return array The array of attributes.
     */
    abstract protected function define_to_api_attributes() : array;

    /**
     * Creates an instance of the entity using attribute data from Degreed's API.
     *
     * This method must be implemented by the concrete entity to extract and
     * transform API data into the entity's internal structure.
     *
     * @param array $data The 'data' array containing 'attributes' from the API.
     * @return static An instance of the entity.
     */
    abstract protected static function from_api_data(array $data) : static;

}
