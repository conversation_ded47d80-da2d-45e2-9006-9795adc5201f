<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_degreed_integration\util;

use local_ssystem\constants\custom_profile_fields;

/**
 * Class user_helper
 *
 * @package    local_degreed_integration
 * @copyright  2025 -
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class user_helper {

    public static function get_occupational_profile(int|object $user) : string {
        global $DB;

        if(is_object($user)){
            $key = 'profile_field_' . custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD;
            if(isset($user->$key)){
                return $user->$key;
            }

            $user = $user->id;
        }

        $sql = "SELECT uid.data
                FROM {user_info_data} uid
                JOIN {user_info_field} uif ON uid.fieldid = uif.id
                WHERE uid.userid = :userid AND uif.shortname = :shortname";

        $params = [
            'userid' => $user,
            'shortname' => custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD,
        ];

        return $DB->get_field_sql($sql, $params) ?: '';
    }

    public static function is_worker(int|object $user) : bool {
        $profile = self::get_occupational_profile($user);
        return $profile == custom_profile_fields::OCCUPATIONAL_PROFILE_WORKER;
    }

    public static function is_outsource(int|object $user) : bool {
        $profile = self::get_occupational_profile($user);
        return $profile == custom_profile_fields::OCCUPATIONAL_PROFILE_OUTSOURCE;
    }

}
