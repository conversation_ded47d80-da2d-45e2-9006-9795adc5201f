<?php

defined('MOODLE_INTERNAL') || die();

use \local_degreed_integration\models\course_sync_record;
use \local_degreed_integration\degreed\entities\skill;
use \local_degreed_integration\degreed\entities\content\course;
use \local_ssystem\constants\custom_course_fields;

class local_degreed_integration_generator extends component_generator_base {

    protected function get_generator() {
        return phpunit_util::get_data_generator();
    }

    public function create_degreed_course(array $data = []) : object {
        $default = [
            'enablecompletion' => true,
            'customfields' => [
                course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME => [
                    'shortname' => course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME,
                    'value' => 1
                ],
                skill::CUSTOM_FIELD_SHORTNAME => [
                    'shortname' => skill::CUSTOM_FIELD_SHORTNAME,
                    'value' => ['skill1','skill2','skill3']
                ],
                course::COURSE_DURATION_FIELD_SHORTNAME => [
                    'shortname' => course::COURSE_DURATION_FIELD_SHORTNAME,
                    'value' => 3600,
                ],
                custom_course_fields::COMPLEXITY_LEVEL => [
                    'shortname' => custom_course_fields::COMPLEXITY_LEVEL,
                    'value' => custom_course_fields::COMPLEXITY_BASIC,
                ],
                custom_course_fields::SOLUTION_FORMAT => [
                    'shortname' => custom_course_fields::SOLUTION_FORMAT,
                    'value' => custom_course_fields::get_solution_format_options()[0],
                ],
            ],
        ];

        foreach ($data as $key => $value) {
            if($key !== "customfields"){
                $default[$key] = $value;
            }
        }

        foreach ($data['customfields'] ?? [] as $customfield) {
            $default['customfields'][$customfield['shortname']] = [
                'shortname' => $customfield['shortname'],
                'value' => $customfield['value'],
            ];
        }

        // Dealing with select custom fields
        if(!empty($default['customfields'][custom_course_fields::COMPLEXITY_LEVEL])){
            $value = $default['customfields'][custom_course_fields::COMPLEXITY_LEVEL]['value'];
            $value = $this->get_customfield_select_option_index(custom_course_fields::COMPLEXITY_LEVEL, $value);
            $default['customfields'][custom_course_fields::COMPLEXITY_LEVEL]['value'] = $value;
        }

        if(!empty($default['customfields'][custom_course_fields::SOLUTION_FORMAT])){
            $value = $default['customfields'][custom_course_fields::SOLUTION_FORMAT]['value'];
            $value = $this->get_customfield_select_option_index(custom_course_fields::SOLUTION_FORMAT, $value);
            $default['customfields'][custom_course_fields::SOLUTION_FORMAT]['value'] = $value;
        }

        // Creating course
        $default['customfields'] = array_values($default['customfields']);
        return $this->get_generator()->create_course($default);
    }

    protected function get_customfield_select_option_index(string $shorname, $value = null) : int {
        global $DB;
        
        if(!$field_raw = $DB->get_record('customfield_field', ['shortname' => $shorname])){
            return 0;
        }

        $field = new \customfield_select\field_controller(0, $field_raw);
        return $field->parse_value($value);
    }

}