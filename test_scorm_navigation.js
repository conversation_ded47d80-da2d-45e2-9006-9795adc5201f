/**
 * Script de Teste para Correção da Navegação SCORM
 * 
 * Execute este script no console do navegador em uma página SCORM
 * para verificar se a correção está funcionando corretamente.
 */

(function() {
    'use strict';
    
    console.log('=== TESTE DA CORREÇÃO DA NAVEGAÇÃO SCORM ===');
    
    // Verifica se estamos em uma página SCORM
    function isScormPage() {
        return document.body.classList.contains('path-mod-scorm') || 
               document.getElementById('scorm_object') !== null ||
               window.location.href.indexOf('/mod/scorm/') !== -1;
    }
    
    if (!isScormPage()) {
        console.error('❌ Este não é uma página SCORM. Execute o teste em uma página de atividade SCORM.');
        return;
    }
    
    console.log('✅ Página SCORM detectada');
    
    // Verifica se as funções SCORM estão disponíveis
    function checkScormFunctions() {
        const checks = [
            { name: 'scorm_fixnav', exists: typeof scorm_fixnav === 'function' },
            { name: 'scorm_buttons', exists: typeof scorm_buttons !== 'undefined' },
            { name: 'scorm_tree_node', exists: typeof scorm_tree_node !== 'undefined' },
            { name: 'M.mod_scorm.connectPrereqCallback', exists: typeof M !== 'undefined' && M.mod_scorm && M.mod_scorm.connectPrereqCallback }
        ];
        
        console.log('\n--- Verificação de Funções SCORM ---');
        checks.forEach(check => {
            console.log(check.exists ? '✅' : '❌', check.name, check.exists ? 'disponível' : 'não encontrado');
        });
        
        return checks.every(check => check.exists);
    }
    
    // Verifica se a API SCORM está disponível
    function checkScormAPI() {
        const apis = [
            { name: 'API (SCORM 1.2)', exists: typeof window.API !== 'undefined' },
            { name: 'API_1484_11 (SCORM 2004)', exists: typeof window.API_1484_11 !== 'undefined' }
        ];
        
        console.log('\n--- Verificação de API SCORM ---');
        apis.forEach(api => {
            console.log(api.exists ? '✅' : '⚠️', api.name, api.exists ? 'disponível' : 'não encontrado');
        });
        
        return apis.some(api => api.exists);
    }
    
    // Verifica se o módulo de correção foi carregado
    function checkNavigationFix() {
        console.log('\n--- Verificação do Módulo de Correção ---');
        
        // Verifica se o módulo AMD foi carregado
        if (typeof require !== 'undefined') {
            try {
                require(['theme_smart/scorm-navigation-fix'], function(ScormNavigationFix) {
                    console.log('✅ Módulo theme_smart/scorm-navigation-fix carregado');
                    if (ScormNavigationFix && typeof ScormNavigationFix.init === 'function') {
                        console.log('✅ Função init disponível no módulo');
                    } else {
                        console.log('❌ Função init não encontrada no módulo');
                    }
                });
            } catch (e) {
                console.log('❌ Erro ao carregar módulo:', e.message);
            }
        } else {
            console.log('⚠️ Sistema require não disponível');
        }
    }
    
    // Verifica o estado atual da navegação
    function checkNavigationState() {
        console.log('\n--- Estado Atual da Navegação ---');
        
        if (typeof scorm_buttons !== 'undefined' && scorm_buttons.length > 0) {
            scorm_buttons.forEach((button, index) => {
                if (button && typeof button.get === 'function') {
                    const disabled = button.get('disabled');
                    const buttonName = index === 0 ? 'Anterior' : index === 1 ? 'Próximo' : `Botão ${index}`;
                    console.log(`${disabled ? '🔒' : '🔓'} ${buttonName}: ${disabled ? 'desabilitado' : 'habilitado'}`);
                }
            });
        } else {
            console.log('❌ scorm_buttons não encontrado ou vazio');
        }
    }
    
    // Testa a função de atualização da navegação
    function testNavigationUpdate() {
        console.log('\n--- Teste de Atualização da Navegação ---');
        
        if (typeof scorm_fixnav === 'function') {
            console.log('🔄 Executando scorm_fixnav()...');
            try {
                scorm_fixnav();
                console.log('✅ scorm_fixnav() executado com sucesso');
                
                // Verifica o estado após a atualização
                setTimeout(() => {
                    console.log('\n--- Estado Após Atualização ---');
                    checkNavigationState();
                }, 500);
            } catch (e) {
                console.log('❌ Erro ao executar scorm_fixnav():', e.message);
            }
        } else {
            console.log('❌ scorm_fixnav não está disponível');
        }
    }
    
    // Simula conclusão de módulo (apenas para teste)
    function simulateCompletion() {
        console.log('\n--- Simulação de Conclusão de Módulo ---');
        
        if (window.API && typeof window.API.LMSSetValue === 'function') {
            console.log('🔄 Simulando conclusão via SCORM 1.2...');
            try {
                window.API.LMSSetValue('cmi.core.lesson_status', 'completed');
                window.API.LMSCommit('');
                console.log('✅ Simulação de conclusão executada');
            } catch (e) {
                console.log('❌ Erro na simulação:', e.message);
            }
        } else if (window.API_1484_11 && typeof window.API_1484_11.SetValue === 'function') {
            console.log('🔄 Simulando conclusão via SCORM 2004...');
            try {
                window.API_1484_11.SetValue('cmi.completion_status', 'completed');
                window.API_1484_11.Commit('');
                console.log('✅ Simulação de conclusão executada');
            } catch (e) {
                console.log('❌ Erro na simulação:', e.message);
            }
        } else {
            console.log('⚠️ API SCORM não disponível para simulação');
        }
    }
    
    // Executa todos os testes
    function runAllTests() {
        const scormFunctionsOk = checkScormFunctions();
        const scormApiOk = checkScormAPI();
        
        checkNavigationFix();
        checkNavigationState();
        
        if (scormFunctionsOk) {
            testNavigationUpdate();
        }
        
        console.log('\n--- Resumo dos Testes ---');
        console.log(scormFunctionsOk ? '✅' : '❌', 'Funções SCORM:', scormFunctionsOk ? 'OK' : 'FALHA');
        console.log(scormApiOk ? '✅' : '❌', 'API SCORM:', scormApiOk ? 'OK' : 'FALHA');
        
        console.log('\n--- Comandos Disponíveis ---');
        console.log('Para testar manualmente:');
        console.log('- testNavigationUpdate(): Testa atualização da navegação');
        console.log('- simulateCompletion(): Simula conclusão de módulo');
        console.log('- checkNavigationState(): Verifica estado atual da navegação');
    }
    
    // Disponibiliza funções globalmente para teste manual
    window.testNavigationUpdate = testNavigationUpdate;
    window.simulateCompletion = simulateCompletion;
    window.checkNavigationState = checkNavigationState;
    
    // Executa os testes
    runAllTests();
    
})();
