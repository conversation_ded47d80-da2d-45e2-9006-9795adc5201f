<?php

namespace local_degreed_integration\degreed\entities\pathway;

use \local_degreed_integration\degreed\interfaces\degreed_entity_interface;
use \local_degreed_integration\degreed\traits\degreed_date_trait;
use \local_degreed_integration\degreed\traits\degreed_duration_trait;
use \local_degreed_integration\degreed\entities\skill;
use \local_degreed_integration\models\entity_sync_record;
use \local_degreed_integration\models\course_sync_record;
use \tool_lfxp\helpers\custom_fields\course\custom_course_field;
use \local_ssystem\constants\custom_course_fields;

use \local_degreed_integration\exceptions\degreed_integration_exception;
use \moodle_url;
use \coding_exception;
use local_degreed_integration\models\trail_sync_record;
use local_degreed_integration\util\image_helper;

class pathway implements degreed_entity_interface {

    use degreed_date_trait, degreed_duration_trait;

    public ?string $id;
    public string $title;
    public ?string $summary = null;
    public string $visibility; // Public, Author, Group, ProfileVisible , Unkown
    public bool $is_endorsed = false;
    public bool $share_author_permission = false;
    public bool $header_image_disabled = false;
    public bool $duration_display_disabled = false;
    public ?string $image_url = null;
    public ?array $sections = null;

    /** @var \local_degreed_integration\degreed\entities\skill[] */
    protected array $skills = [];

    protected ?int $courseid;
    protected ?object $customfields = null;

    protected function __construct(array|object $data = []){
        if(is_object($data)){
            $data = (array) $data;
        }

        foreach ($data as $key => $value) {
            if(property_exists($this, $key)){
                $this->$key = $value;
            }
        }
    }

     /**
     * JSON following the structure
     * {
     *      "data" : {
     *          "attributes" : {...}
*           }
     * }
     *
     * @return string
     */
    public function to_api() : string {
        $data = [
            'data' => [
                'attributes' => [
                    'title' => $this->title,
                    'summary' => $this->summary,
                    'visibility' => $this->visibility,
                    'is-endorsed' => $this->is_endorsed,
                    'share-author-permission' => $this->share_author_permission,
                    'header-image-disabled' => $this->header_image_disabled,
                    'duration-display-disabled' => $this->duration_display_disabled,
                    'image-url' => $this->image_url,
                ],
            ]
        ];

        if(!empty($this->sections)){
            $data['data']['attributes']['sections'] = $this->sections;
        }

        return json_encode($data);
    }

    public static function from_api(string $response) : static {
        $response = json_decode($response, true);

        if(empty($response['data']) || empty($response['data']['attributes'])){
            throw new \InvalidArgumentException("Malformed Degreed's API Response");
        }

        $attributes = $response['data']['attributes'];

        $instance = new static([
            'id' => $response['data']['id'],
            'title' => $attributes['title'] ?? null,
            'summary' => $attributes['summary'] ?? null,
            'is_endorsed' => $attributes['is-endorsed'] ?? false,
            'share_author_permission' => $attributes['share-author-permission'] ?? false,
            'header_image_disabled' => $attributes['header-image-disabled'] ?? false,
            'duration_display_disabled' => $attributes['duration-display-disabled'] ?? false,
            'image_url' => $attributes['image-url'] ?? null,
            'sections' => $attributes['sections'] ?? [],
        ]);

        return $instance;
    }

    protected function load_skills(){
        if(empty($this->customfields)){
            $this->load_customfields();
        }
        $this->skills = skill::from_course_customfields($this->customfields);
    }

    protected function load_customfields(){
        $course = (object) ['id' => $this->courseid];
        custom_course_field::load_custom_fields($course);

        $this->customfields = $course->customfields;
    }

    public function get_skills() : array {
        if(empty($this->skills)){
            $this->load_skills();
        }
        return $this->skills;
    }


    public static function from_trail_sync_record(trail_sync_record $sync_record) : static {
        if(!$instance = $sync_record->get_course()){
            throw new degreed_integration_exception('exception:missing_course');
        }

        $trail = new static([
            'id' => $sync_record->get('externalid') ?: null,
            'courseid' => $instance->id,
            'title' => $instance->fullname,
            'summary' => $instance->summary,
            'image_url' => image_helper::get_course_image_url($instance),
            'sections' => null, // Change?
        ]);

        return $trail;
    }
}
